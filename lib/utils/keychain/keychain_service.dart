import 'dart:convert';
import 'dart:developer';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:toii_social/model/credentials/credentials.dart';

class KeychainService {
  late final FlutterSecureStorage _storage;
  String isEnableBio = "key_Enable_Bio";
  String keyEmail = "key_Email";
  String keyPassword = "key_Password";
  String keyAddress = "key_Address";
  String _address = "address";
  KeychainService._privateConstructor() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  static final KeychainService instance = KeychainService._privateConstructor();

  init() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  Future<String> getPasswordUser() async {
    var password = await _storage.read(key: keyPassword) ?? "";
    return password;
  }

  Future<String> getUserName() async {
    var email = await _storage.read(key: keyEmail) ?? "";
    return email;
  }

  void setUserName(String email) async {
    await _storage.write(key: keyEmail, value: email);
  }

  void setPassword(String password) async {
    await _storage.write(key: keyPassword, value: password);
  }

  void setEnableBio(bool value) async {
    await _storage.write(key: isEnableBio, value: value ? "1" : "0");
  }

  Future<bool> isEnableBiometric() async {
    var isEnable = await _storage.read(key: isEnableBio) ?? "";
    return int.tryParse(isEnable) == 1 ? true : false;
  }

  void clearAll() async {
    await _storage.deleteAll();
  }

  void setWallet({required Credentials credentials}) async {
    try {
      log("privateKeyHex create ${credentials.privateKeyHex}");
      log("address create ${credentials.address}");
    await _storage.write(key: _address, value: credentials.address);
      var address = await _storage.read(key: keyAddress) ?? "";
      Map<String, dynamic> valueMap =
          address.isNotEmpty ? jsonDecode(address) : {};

      valueMap[credentials.address] = credentials.privateKeyHex;
      await _storage.write(key: keyAddress, value: json.encode(valueMap));
    } catch (e) {
      log("Error setting key to card: $e");
    }
  }

  Future<String?> getAddress() async {
    try {
      var address = await _storage.read(key: _address) ?? "";
      return address;
    } catch (e) {
      log("Error getting address: $e");
    } catch (e) {
      log("Error getting address: $e");
    }
    return null;
  }
}
