# BaseTabbarHome Widget

A customizable tabbar widget designed for the "For You" / "Following" style interface, featuring an underline indicator and smooth animations.

## Features

- ✅ Customizable tab labels
- ✅ Smooth tab switching animations
- ✅ Customizable colors and styles
- ✅ Underline indicator
- ✅ Responsive design
- ✅ Easy integration with PageView or IndexedStack

## Basic Usage

```dart
import 'package:toii_social/widget/tabbar/base_tabbar_home.dart';

BaseTabbarHome(
  tabs: ['For You', 'Following'],
  initialIndex: 0,
  onTabChanged: (index) {
    // Handle tab change
    print('Tab changed to: $index');
  },
)
```

## Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `tabs` | `List<String>` | **Required** | List of tab labels |
| `initialIndex` | `int` | `0` | Initial selected tab index |
| `onTabChanged` | `Function(int)?` | `null` | Callback when tab is changed |
| `selectedColor` | `Color?` | `context.themeData.textPrimary` | Color for selected tab text |
| `unselectedColor` | `Color?` | `context.themeData.textSecondary` | Color for unselected tab text |
| `indicatorColor` | `Color?` | `context.themeData.primaryGreen500` | Color for underline indicator |
| `selectedTextStyle` | `TextStyle?` | `null` | Custom text style for selected tab |
| `unselectedTextStyle` | `TextStyle?` | `null` | Custom text style for unselected tab |
| `indicatorHeight` | `double?` | `2.0` | Height of underline indicator |
| `padding` | `EdgeInsetsGeometry?` | `EdgeInsets.symmetric(horizontal: 16)` | Container padding |
| `tabPadding` | `EdgeInsetsGeometry?` | `EdgeInsets.symmetric(vertical: 12)` | Individual tab padding |

## Advanced Usage

### With Custom Styling

```dart
BaseTabbarHome(
  tabs: ['For You', 'Following'],
  selectedColor: Colors.black,
  unselectedColor: Colors.grey,
  indicatorColor: const Color(0xff82AD0A),
  indicatorHeight: 3.0,
  selectedTextStyle: const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
  ),
  unselectedTextStyle: const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  ),
  padding: const EdgeInsets.symmetric(horizontal: 20),
  tabPadding: const EdgeInsets.symmetric(vertical: 16),
  onTabChanged: (index) {
    // Handle tab change
  },
)
```

### With PageView Integration

```dart
class MyTabPage extends StatefulWidget {
  @override
  _MyTabPageState createState() => _MyTabPageState();
}

class _MyTabPageState extends State<MyTabPage> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BaseTabbarHome(
          tabs: ['For You', 'Following'],
          initialIndex: _currentIndex,
          onTabChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
            _pageController.animateToPage(
              index,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
        ),
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: [
              // Your page content here
            ],
          ),
        ),
      ],
    );
  }
}
```

## Design Specifications

The widget follows the design shown in the reference image:
- Clean, minimal design
- Bold text for selected tab
- Regular text for unselected tab
- Green underline indicator for selected tab
- Smooth animations between tabs

## Examples

See `example_usage.dart` for complete working examples including:
- Basic usage with IndexedStack
- Advanced usage with PageView and swipe gestures
- Custom styling examples
