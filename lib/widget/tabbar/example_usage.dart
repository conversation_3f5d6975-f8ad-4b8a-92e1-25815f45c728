import 'package:flutter/material.dart';
import 'package:toii_social/widget/tabbar/base_tabbar_home.dart';

/// Example usage of BaseTabbarHome widget
/// This demonstrates how to use the tabbar similar to the "For You" / "Following" design
class ExampleTabbarUsage extends StatefulWidget {
  const ExampleTabbarUsage({super.key});

  @override
  State<ExampleTabbarUsage> createState() => _ExampleTabbarUsageState();
}

class _ExampleTabbarUsageState extends State<ExampleTabbarUsage> {
  int _currentTabIndex = 0;
  
  final List<String> _tabs = ['For You', 'Following'];
  
  final List<Widget> _tabViews = [
    const Center(
      child: Text(
        'For You Content',
        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
      ),
    ),
    const Center(
      child: Text(
        'Following Content',
        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text('Tabbar Example'),
      ),
      body: Column(
        children: [
          // Custom Tabbar
          BaseTabbarHome(
            tabs: _tabs,
            initialIndex: _currentTabIndex,
            onTabChanged: (index) {
              setState(() {
                _currentTabIndex = index;
              });
            },
            // Optional customizations:
            // selectedColor: Colors.black,
            // unselectedColor: Colors.grey,
            // indicatorColor: const Color(0xff82AD0A),
            // indicatorHeight: 3.0,
            // padding: const EdgeInsets.symmetric(horizontal: 20),
            // tabPadding: const EdgeInsets.symmetric(vertical: 16),
          ),
          
          // Tab Content
          Expanded(
            child: IndexedStack(
              index: _currentTabIndex,
              children: _tabViews,
            ),
          ),
        ],
      ),
    );
  }
}

/// Alternative usage with PageView for swipeable tabs
class ExampleTabbarWithPageView extends StatefulWidget {
  const ExampleTabbarWithPageView({super.key});

  @override
  State<ExampleTabbarWithPageView> createState() => _ExampleTabbarWithPageViewState();
}

class _ExampleTabbarWithPageViewState extends State<ExampleTabbarWithPageView> {
  int _currentTabIndex = 0;
  late PageController _pageController;
  
  final List<String> _tabs = ['For You', 'Following'];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text('Tabbar with PageView'),
      ),
      body: Column(
        children: [
          // Custom Tabbar
          BaseTabbarHome(
            tabs: _tabs,
            initialIndex: _currentTabIndex,
            onTabChanged: (index) {
              setState(() {
                _currentTabIndex = index;
              });
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
          ),
          
          // PageView Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentTabIndex = index;
                });
              },
              children: [
                Container(
                  color: Colors.blue.shade50,
                  child: const Center(
                    child: Text(
                      'For You Page\n(Swipe to navigate)',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                Container(
                  color: Colors.green.shade50,
                  child: const Center(
                    child: Text(
                      'Following Page\n(Swipe to navigate)',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
