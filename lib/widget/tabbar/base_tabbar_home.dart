import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/colors.dart';

class BaseTabbarHome extends StatefulWidget {
  final List<String> tabs;
  final int initialIndex;
  final Function(int index)? onTabChanged;
  final Color? selectedColor;
  final Color? unselectedColor;
  final Color? indicatorColor;
  final TextStyle? selectedTextStyle;
  final TextStyle? unselectedTextStyle;
  final double? indicatorHeight;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? tabPadding;

  const BaseTabbarHome({
    super.key,
    required this.tabs,
    this.initialIndex = 0,
    this.onTabChanged,
    this.selectedColor,
    this.unselectedColor,
    this.indicatorColor,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.indicatorHeight = 2.0,
    this.padding,
    this.tabPadding,
  });

  @override
  State<BaseTabbarHome> createState() => _BaseTabbarHomeState();
}

class _BaseTabbarHomeState extends State<BaseTabbarHome>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _tabController = TabController(
      length: widget.tabs.length,
      vsync: this,
      initialIndex: widget.initialIndex,
    );
    _tabController.addListener(_handleTabSelection);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentIndex = _tabController.index;
      });
      widget.onTabChanged?.call(_tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedColor = widget.selectedColor ?? context.themeData.textPrimary;
    final unselectedColor =
        widget.unselectedColor ?? context.themeData.textSecondary;
    final indicatorColor =
        widget.indicatorColor ?? context.themeData.primaryGreen500;

    return Container(
      padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        tabs:
            widget.tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = index == _currentIndex;

              return Container(
                padding:
                    widget.tabPadding ??
                    const EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  tab,
                  style:
                      isSelected
                          ? (widget.selectedTextStyle ??
                              TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: selectedColor,
                              ))
                          : (widget.unselectedTextStyle ??
                              TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: unselectedColor,
                              )),
                ),
              );
            }).toList(),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: widget.indicatorHeight!,
            color: indicatorColor,
          ),
          insets: const EdgeInsets.symmetric(horizontal: 0),
        ),
        indicatorSize: TabBarIndicatorSize.label,
        dividerColor: Colors.transparent,
        labelPadding: EdgeInsets.zero,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
      ),
    );
  }
}
