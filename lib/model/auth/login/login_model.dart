import 'package:json_annotation/json_annotation.dart';

part 'login_model.g.dart';

@JsonSerializable()
class LoginRequestModel {
  const LoginRequestModel({
    this.email,
    required this.password,
    this.appType = "toii-social",
    // this.phoneNumber,
  });

  final String appType;
  final String? email;
  final String? password;

  // @Json<PERSON><PERSON>(name: 'phoneNumber')
  // final String? phoneNumber;

  factory LoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);
}

@JsonSerializable()
class LoginModel {
  const LoginModel({
    required this.accessToken,
    required this.expiredAt,
    this.isNewUser,
    required this.refreshToken,
    this.tmpToken
  });

  @JsonKey(name: 'access_token')
  final String accessToken;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expired_at')
  final String expiredAt;

  @J<PERSON><PERSON><PERSON>(name: 'is_new_user')
  final bool? isNewUser;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  final String refreshToken;

  @J<PERSON><PERSON><PERSON>(name: 'tmp_token')
  final String? tmpToken;

  factory LoginModel.fromJson(Map<String, dynamic> json) =>
      _$LoginModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginModelToJson(this);
}
