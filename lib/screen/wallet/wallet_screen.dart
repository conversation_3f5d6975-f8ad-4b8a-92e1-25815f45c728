import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/cubit/wallet/wallet_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/wallet/widgets/add_custom_token.dart';
import 'package:toii_social/screen/wallet/widgets/bottom_sheet_network.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class WalletScreen extends StatelessWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        return BlocProvider(
          create: (context) => WalletCubit()..loadWallet(),
          child: const _WalletScreenContent(),
        );
      },
    );
  }
}

class _WalletScreenContent extends StatefulWidget {
  const _WalletScreenContent();

  @override
  State<_WalletScreenContent> createState() => _WalletScreenContentState();
}

class _WalletScreenContentState extends State<_WalletScreenContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showNFTDetection = true;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      //    backgroundColor: theme.neutral50,
      title: Row(
        children: [
          Text(
            'My Wallet',
            style: headlineMedium.copyWith(
              color: themeData.neutral800,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      body: BlocBuilder<WalletCubit, WalletState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // Wallet Address Header
                _buildWalletAddressHeader(),

                // Balance Card
                _buildBalanceCard(context),

                const SizedBox(height: 24),

                // Action Buttons
                _buildActionButtons(context),

                const SizedBox(height: 16),
                Container(height: 2, color: themeData.neutral200),
                const SizedBox(height: 16),
                _buildTokensNFTsSection(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWalletAddressHeader() {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                shortenAddress(state.userModel?.walletAddress ?? ''),
                style: bodyMedium.copyWith(
                  color: themeData.neutral800,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap:
                    () => _copyToClipboard(
                      context,
                      state.userModel?.walletAddress ?? '',
                    ),
                child: Icon(Icons.copy, size: 16, color: themeData.neutral800),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBalanceCard(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Stack(
            children: [
              Assets.images.bgWallet1.image(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 26,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            showBottomSheetNetwork(context);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 6,
                            ),

                            decoration: BoxDecoration(
                              color: themeData.white600,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: themeData.white900,
                                width: 1,
                              ),
                            ),

                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Token icon
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                    color: Colors.black,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Center(
                                    child: Text(
                                      'T',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  state.currentActiveChain.name ?? "",
                                  style: titleMedium.copyColor(
                                    themeData.primaryGreen900,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                SvgPicture.asset(
                                  Assets.icons.icArrowDown.path,
                                  width: 24,
                                  height: 24,
                                  colorFilter: ColorFilter.mode(
                                    themeData.primaryGreen900,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Spacer(),

                        GestureDetector(
                            onTap: () {
                            context.push(RouterEnums.scanQr.routeName);
                          },
                          child: SvgPicture.asset(Assets.icons.icScanQr.path)),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Balance amount
                    Text(
                      '\$403.68',
                      style: headlineLarge.copyColor(themeData.black900),
                    ),

                    const SizedBox(height: 4),

                    Text(
                      '= 0.0034TOII',
                      style: labelLarge.copyColor(themeData.black900),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            path: Assets.icons.icSend.path,
            label: 'Send',
            onTap: () => _showSendDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icReceive.path,
            label: 'Receive',
            onTap: () => _showReceiveDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icEarn.path,
            label: 'Earn',
            onTap: () => _showHistoryScreen(context),
          ),
          _buildActionButton(
            path: Assets.icons.icDapp.path,
            label: 'DApps',
            onTap: () => _showDAppsScreen(context),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String path,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.neutral100,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Center(child: SvgPicture.asset(path)),
          ),
          const SizedBox(height: 8),
          Text(label, style: bodyMedium.copyColor(themeData.neutral800)),
        ],
      ),
    );
  }

  Widget _buildTokensNFTsSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Custom Tab bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                _buildCustomTabButton('Tokens', 0),
                const SizedBox(width: 32),
                _buildCustomTabButton('NFTs', 1),
              ],
            ),
          ),

          // Tab content
          _selectedTabIndex == 0
              ? _buildTokensTab(context)
              : _buildNFTsTab(context),
        ],
      ),
    );
  }

  Widget _buildCustomTabButton(String title, int index) {
    final theme = Theme.of(context);
    final isSelected = _selectedTabIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTabIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? theme.primaryGreen500 : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          title,
          style: bodyMedium.copyWith(
            color: isSelected ? theme.textPrimary : theme.textSecondary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTokensTab(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletState>(
      builder: (context, state) {
        final theme = Theme.of(context);
        return Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: OutlinedButton.icon(
                onPressed: () => _showAddTokensDialog(context),
                icon: const Icon(Icons.add, size: 20),
                label: const Text('Add tokens'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: theme.textSecondary,
                  side: BorderSide(color: theme.neutral300),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            // Divider
            Container(
              height: 1,
              color: theme.neutral200,
              margin: const EdgeInsets.symmetric(horizontal: 16),
            ),

            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder:
                  (context, index) => Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // Token icon
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Color(0xFF4ADE80),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              state.tokens[index].symbol?[0] ?? '',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Token info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                state.tokens[index].symbol ?? "",
                                style: bodyMedium.copyWith(
                                  color: theme.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                state.tokens[index].name,
                                style: bodySmall.copyWith(
                                  color: const Color(0xFF4ADE80),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Token balance
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${state.tokens[index].balance ?? 0} ${state.tokens[index].symbol}',
                              style: bodyMedium.copyWith(
                                color: theme.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '\$0',
                              style: bodySmall.copyWith(
                                color: theme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemCount: state.tokens.length,
            ),

            // Add tokens button
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildNFTsTab(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // NFT detection banner
        if (_showNFTDetection)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.neutral50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'NFT detection',
                        style: bodyMedium.copyWith(
                          color: theme.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Allow Toii to automatically detect and display NFTs in your wallet.',
                        style: bodySmall.copyWith(color: theme.textSecondary),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showNFTDetection = false;
                    });
                  },
                  icon: Icon(Icons.close, color: theme.textSecondary, size: 20),
                ),
              ],
            ),
          ),

        // Import NFT button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showImportNFTDialog(context),
              icon: const Icon(Icons.add, size: 20),
              label: const Text('Import NFT'),
              style: OutlinedButton.styleFrom(
                foregroundColor: theme.textSecondary,
                side: BorderSide(color: theme.neutral300),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  // Dialog and utility methods
  void _copyToClipboard(BuildContext context, String address) {
    Clipboard.setData(ClipboardData(text: address));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Address copied to clipboard')),
    );
  }

  void _showSendDialog(BuildContext context) {
    context.push(
      RouterEnums.sendToken.routeName
     // RouterEnums.selectedToken.routeName,
      //  extra: context.read<ProfileCubit>().state.userModel?.walletAddress,
    );
    // Implement send dialog
  }

  void _showReceiveDialog(BuildContext context) {
    context.push(
      RouterEnums.receiveToken.routeName,
      extra: context.read<ProfileCubit>().state.userModel?.walletAddress,
    );
  }

  void _showHistoryScreen(BuildContext context) {
    // Navigate to history screen
  }

  void _showDAppsScreen(BuildContext context) {
    // Navigate to DApps screen
  }

  void _showAddTokensDialog(BuildContext context) {
    showBottomSheetAddCustomToken(
      context,
      context.read<WalletCubit>().state.currentActiveChain,
    );
  }

  void _showImportNFTDialog(BuildContext context) {
    // Implement import NFT dialog
  }
}
