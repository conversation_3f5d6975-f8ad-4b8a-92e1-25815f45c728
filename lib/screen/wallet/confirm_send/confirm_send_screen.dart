import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class ConfirmSendScreen extends StatefulWidget {
  final String address;
  final String amount;
  final String token;
  final String recipientName;
  
  const ConfirmSendScreen({
    super.key,
    this.address = '0x10bfco3eregerohituhe329857493Z',
    this.amount = '0',
    this.token = 'ETH',
    this.recipientName = '<PERSON>',
  });

  @override
  State<ConfirmSendScreen> createState() => _ConfirmSendScreenState();
}

class _ConfirmSendScreenState extends State<ConfirmSendScreen> {
  final String _networkFee = '0.0005';
  final String _networkFeeUsd = '\$12';
  final String _senderName = '<PERSON>';
  final String _senderAddress = '0x10bfco3eregerohituhe329857493Z';

  String get _amountUsd {
    // Calculate USD value based on amount
    final amount = double.tryParse(widget.amount) ?? 0;
    final usd = amount * 42920.0; // Example rate
    return '\$${usd.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseScaffold(
      title: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(
              Icons.arrow_back_ios,
              color: theme.textPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Confirm send',
            style: headlineMedium.copyWith(
              color: theme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Amount Section
                  _buildAmountSection(context),
                  
                  const SizedBox(height: 24),
                  
                  // Recipient Address Section
                  _buildRecipientSection(context),
                  
                  const SizedBox(height: 24),
                  
                  // Network Section
                  _buildNetworkSection(context),
                  
                  const SizedBox(height: 24),
                  
                  // Network Fee Section
                  _buildNetworkFeeSection(context),
                  
                  const SizedBox(height: 24),
                  
                  // Wallet Section
                  _buildWalletSection(context),
                ],
              ),
            ),
          ),
          
          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildAmountSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Amount',
          style: bodyMedium.copyWith(
            color: theme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            // Token Icon
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: theme.primaryGreen500,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  'T',
                  style: bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Token Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.token,
                    style: bodyMedium.copyWith(
                      color: theme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Toii',
                    style: bodySmall.copyWith(
                      color: theme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Amount
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '- ${widget.amount}',
                  style: bodyMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _amountUsd,
                  style: bodySmall.copyWith(
                    color: theme.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecipientSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recipient address',
          style: bodyMedium.copyWith(
            color: theme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.neutral200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.recipientName,
                style: bodyMedium.copyWith(
                  color: theme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                widget.address,
                style: bodySmall.copyWith(
                  color: theme.textSecondary,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNetworkSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Network',
          style: bodyMedium.copyWith(
            color: theme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        
        Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: theme.primaryGreen500,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  'T',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              widget.token,
              style: bodyMedium.copyWith(
                color: theme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNetworkFeeSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Network fee',
          style: bodyMedium.copyWith(
            color: theme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '$_networkFee ${widget.token}',
              style: bodyMedium.copyWith(
                color: theme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              _networkFeeUsd,
              style: bodySmall.copyWith(
                color: theme.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWalletSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Wallet',
          style: bodyMedium.copyWith(
            color: theme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.neutral200),
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: theme.neutral300,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  color: theme.textSecondary,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Wallet Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _senderName,
                      style: bodyMedium.copyWith(
                        color: theme.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _senderAddress,
                      style: bodySmall.copyWith(
                        color: theme.textSecondary,
                        fontFamily: 'monospace',
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: theme.neutral200),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Reject Button
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _rejectTransaction(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeData.neutral800,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Reject',
                    style: bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Confirm Button
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _confirmTransaction(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.primaryGreen500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Confirm',
                    style: bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Bottom Indicator
          Container(
            width: 134,
            height: 5,
            decoration: BoxDecoration(
              color: theme.textPrimary,
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
        ],
      ),
    );
  }

  void _rejectTransaction(BuildContext context) {
    // Handle rejection
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void _confirmTransaction(BuildContext context) {
    // Handle confirmation - show loading, process transaction, etc.
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(Theme.of(context).primaryGreen500),
            ),
            const SizedBox(height: 16),
            const Text('Processing transaction...'),
          ],
        ),
      ),
    );
    
    // Simulate transaction processing
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.of(context).pop(); // Close loading dialog
      Navigator.of(context).popUntil((route) => route.isFirst); // Go back to wallet
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Transaction sent successfully!'),
          backgroundColor: Theme.of(context).primaryGreen500,
        ),
      );
    });
  }
}