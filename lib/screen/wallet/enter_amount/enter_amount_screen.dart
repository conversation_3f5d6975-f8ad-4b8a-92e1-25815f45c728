import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class EnterAmountScreen extends StatefulWidget {
  final String address;

  const EnterAmountScreen({super.key, required this.address});

  @override
  State<EnterAmountScreen> createState() => _EnterAmountScreenState();
}

class _EnterAmountScreenState extends State<EnterAmountScreen> {
  final TextEditingController _amountController = TextEditingController();
  final FocusNode _amountFocusNode = FocusNode();
  final String _balance = '0.0034';
  final String _tokenSymbol = 'TOII';
  final double _usdRate = 42920.0; // Example rate for TOII to USD

  bool get _canContinue =>
      _amountController.text.isNotEmpty &&
      double.tryParse(_amountController.text) != null;

  String get _usdValue {
    if (_amountController.text.isEmpty) return '\$0.00';
    final amount = double.tryParse(_amountController.text) ?? 0;
    final usd = amount * _usdRate;
    return '= \$${usd.toStringAsFixed(2)}';
  }

  @override
  void initState() {
    super.initState();
    _amountController.addListener(() {
      setState(() {});
    });
    // Auto-focus the text field to show keyboard
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _amountFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _amountFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseScaffold(
      title: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(
              Icons.arrow_back_ios,
              color: theme.textPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Enter Amount',
            style: headlineMedium.copyWith(
              color: theme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 80),

                  // Amount Display
                  _buildAmountDisplay(context),

                  const SizedBox(height: 80),

                  // Balance Section
                  _buildBalanceSection(context),

                  const SizedBox(height: 24),

                  // Continue Button
                  _buildContinueButton(context),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountDisplay(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Hidden text field for input
        Opacity(
          opacity: 0.01, // Nearly invisible but still functional
          child: TextField(
            controller: _amountController,
            focusNode: _amountFocusNode,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            style: const TextStyle(color: Colors.transparent),
            decoration: const InputDecoration(border: InputBorder.none),
            showCursor: false,
          ),
        ),

        // Visual amount display
        GestureDetector(
          onTap: () => _amountFocusNode.requestFocus(),
          child: Column(
            children: [
              // Cursor or amount
              if (_amountController.text.isEmpty) ...[
                Container(width: 2, height: 40, color: theme.primaryGreen500),
              ] else ...[
                Text(
                  '${_amountController.text} $_tokenSymbol',
                  style: displayMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  _usdValue,
                  style: bodyLarge.copyWith(color: theme.textSecondary),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.neutral100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Token Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: theme.primaryGreen500,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'T',
                style: bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Balance Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Balance',
                  style: bodySmall.copyWith(color: theme.textSecondary),
                ),
                Text(
                  '$_balance $_tokenSymbol',
                  style: bodyMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Max Button
          GestureDetector(
            onTap: () => _setMaxAmount(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: theme.primaryGreen500),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Max',
                style: bodySmall.copyWith(
                  color: theme.primaryGreen500,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _canContinue ? () => _continue(context) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              _canContinue ? theme.primaryGreen500 : theme.neutral300,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
          disabledBackgroundColor: theme.neutral300,
        ),
        child: Text(
          'Continue',
          style: bodyLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _setMaxAmount() {
    _amountController.text = _balance;
    _amountController.selection = TextSelection.fromPosition(
      TextPosition(offset: _amountController.text.length),
    );
  }

  void _continue(BuildContext context) {
    // Navigate to confirmation screen
    context.push(
      RouterEnums.confirmSendToken.routeName,
      extra: {
        'address': widget.address,
        'amount': _amountController.text,
        'token': _tokenSymbol,
      },
    );
  }
}

// // Placeholder for confirmation screen
// class TransactionConfirmationScreen extends StatelessWidget {
//   final String address;
//   final String amount;
//   final String token;

//   const TransactionConfirmationScreen({
//     super.key,
//     required this.address,
//     required this.amount,
//     required this.token,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Confirmation')),
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [Text('Send $amount $token'), Text('To: $address')],
//         ),
//       ),
//     );
//   }
// }
