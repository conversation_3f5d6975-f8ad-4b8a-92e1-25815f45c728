import 'package:flutter/material.dart';

import '../../../../widget/colors/text_style.dart';

class DialogConfirmDeletePost extends StatelessWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onDelete;
  final String title;
  final String description;

  const DialogConfirmDeletePost({
    super.key,
    this.onCancel,
    this.onDelete,
    this.title = 'Delete comment',
    this.description = 'Are you sure you want to delete your comment?',
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 270,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Content Section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 19),
                child: Column(
                  children: [
                    // Title
                    SizedBox(
                      width: 238,
                      child: Text(
                        title,
                        style: titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 17,
                          height: 1.29,
                          letterSpacing: -0.41,
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 2),
                    // Description
                    SizedBox(
                      width: 238,
                      child: Text(
                        description,
                        style: bodySmall.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: 13,
                          height: 1.23,
                          letterSpacing: -0.08,
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              // Action Row
              Container(
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: const Color(0xFF3C3C43).withOpacity(0.29),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    // Cancel Button
                    Expanded(
                      child: GestureDetector(
                        onTap: onCancel ?? () => Navigator.of(context).pop(),
                        child: SizedBox(
                          height: 44,
                          child: Center(
                            child: Text(
                              'Cancel',
                              style: titleMedium.copyWith(
                                fontWeight: FontWeight.w400,
                                fontSize: 17,
                                height: 1.29,
                                letterSpacing: -0.41,
                                color: const Color(0xFF007AFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Vertical Divider
                    Container(
                      width: 0.5,
                      height: 43.5,
                      color: const Color(0xFF3C3C43).withOpacity(0.29),
                    ),
                    // Delete Button
                    Expanded(
                      child: GestureDetector(
                        onTap: onDelete ?? () => Navigator.of(context).pop(),
                        child: SizedBox(
                          height: 44,
                          child: Center(
                            child: Text(
                              'Delete',
                              style: titleMedium.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 17,
                                height: 1.29,
                                letterSpacing: -0.41,
                                color: const Color(0xFF007AFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper function to show the dialog
Future<bool?> showDialogConfirmDeletePost({
  required BuildContext context,
  String title = 'Delete comment',
  String description = 'Are you sure you want to delete your comment?',
  VoidCallback? onCancel,
  VoidCallback? onDelete,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: true,
    builder:
        (context) => DialogConfirmDeletePost(
          title: title,
          description: description,
          onCancel: onCancel,
          onDelete: onDelete,
        ),
  );
}
