import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class DetailPreviewImgArg {
  final List<String> images;
  final int initialIndex;
  final PostModel? post;

  DetailPreviewImgArg({
    required this.images,
    required this.initialIndex,
    this.post,
  });
}

class DetailPreviewImg extends StatefulWidget {
  final DetailPreviewImgArg arg;

  const DetailPreviewImg({super.key, required this.arg});

  @override
  _DetailPreviewImgState createState() => _DetailPreviewImgState();
}

class _DetailPreviewImgState extends State<DetailPreviewImg> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.arg.initialIndex;
    _pageController = PageController(initialPage: widget.arg.initialIndex);
  }

  bool _hasMoreLines() {
    final text = widget.arg.post?.content ?? '';
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: bodyLarge),
      maxLines: 2,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(
      maxWidth: MediaQuery.of(context).size.width - 32,
    ); // Account for padding
    return textPainter.didExceedMaxLines;
  }

  ImageProvider _getImageProvider(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return NetworkImage(path);
    } else {
      return FileImage(File(path));
    }
  }

  @override
  Widget build(BuildContext context) {
    final double imageHeight = MediaQuery.of(context).size.height * 0.8;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.chevron_left, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Stack(
        children: [
          SizedBox(
            height: imageHeight,
            child: PhotoViewGallery.builder(
              itemCount: widget.arg.images.length,
              pageController: _pageController,
              onPageChanged: (index) => setState(() => _currentIndex = index),
              builder: (context, index) {
                return PhotoViewGalleryPageOptions(
                  imageProvider: _getImageProvider(widget.arg.images[index]),
                  heroAttributes: PhotoViewHeroAttributes(
                    tag: widget.arg.images[index],
                  ),
                  minScale: PhotoViewComputedScale.contained,
                  maxScale: PhotoViewComputedScale.covered * 2,
                );
              },
              loadingBuilder:
                  (context, event) =>
                      Center(child: CircularProgressIndicator()),
            ),
          ),

          /// --- Overlay Like - Comment - Share ---
          if (widget.arg.post != null)
            Positioned(
              bottom: 0,
              left: 16,
              right: 16,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_isExpanded)
                    Container(
                      padding: EdgeInsets.all(12),
                      margin: EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundImage: NetworkImage(
                                  widget.arg.post?.user?.avatar ??
                                      "https://i.pravatar.cc/150?img=3",
                                ),
                              ),
                              SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.arg.post?.user?.username ??
                                        widget.arg.post?.user?.fullName ??
                                        '',
                                    style: titleMedium.copyWith(
                                      color: themeData.white900,
                                    ),
                                  ),
                                  Text(
                                    timeAgoSinceDate(
                                      widget.arg.post?.updatedAt ?? '',
                                    ),
                                    style: labelMedium.copyWith(
                                      color: themeData.neutral300,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          if (widget.arg.post?.content.isNotEmpty ?? false)
                            Padding(
                              padding: EdgeInsets.only(top: 12),
                              child: GestureDetector(
                                onTap:
                                    () => setState(
                                      () => _isExpanded = !_isExpanded,
                                    ),
                                child: Text(
                                  widget.arg.post?.content ?? '',
                                  style: bodyLarge.copyWith(
                                    color: themeData.white900,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundImage: NetworkImage(
                                widget.arg.post?.user?.avatar ??
                                    "https://i.pravatar.cc/150?img=3",
                              ),
                            ),
                            SizedBox(width: 8),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.arg.post?.user?.username ??
                                      widget.arg.post?.user?.fullName ??
                                      '',
                                  style: titleMedium.copyWith(
                                    color: themeData.white900,
                                  ),
                                ),
                                Text(
                                  timeAgoSinceDate(
                                    widget.arg.post?.updatedAt ?? '',
                                  ),
                                  style: labelMedium.copyWith(
                                    color: themeData.neutral300,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        if (widget.arg.post?.content.isNotEmpty ?? false)
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                GestureDetector(
                                  onTap:
                                      () => setState(
                                        () => _isExpanded = !_isExpanded,
                                      ),
                                  child: Text(
                                    widget.arg.post?.content ?? '',
                                    style: bodyLarge.copyWith(
                                      color: themeData.white900,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (!_isExpanded && _hasMoreLines())
                                  GestureDetector(
                                    onTap:
                                        () => setState(() {
                                          _isExpanded = true;
                                        }),
                                    child: Padding(
                                      padding: EdgeInsets.only(top: 4),
                                      child: Text(
                                        'See more',
                                        style: bodyMedium.copyWith(
                                          color: themeData.primaryGreen500,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  PostActionBar(
                    post:
                        widget.arg.post ??
                        PostModel(id: '', content: '', reposts: 0, comments: 0),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
