import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/auth/login/login_model.dart';
import 'package:toii_social/model/auth/login_gmail/login_gmail_request_model.dart';
import 'package:toii_social/model/auth/login_wallet/login_wallet.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(const LoginState());
  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void login({required String userName, required String password}) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginRequestModel(
        email: userName.trim().toLowerCase(),
        password: password,
      );
      final result = await _authRepository.login(request);
      SharedPref.setBool(KeyShared.isLogin, true);
      SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
      emit(state.copyWith(status: LoginStatus.success));
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  void loginWithGmail({
    required String accessToken,
    required String idToken,
  }) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginGmailRequestModel(
        accessToken: accessToken,
        idToken: idToken,
      );
      final result = await _authRepository.tokenWithGmail(request);

      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        if (result.data.tmpToken?.isNotEmpty == true) {
          SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
        }

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.social,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  void loginWithApple({required String idToken}) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginAppleRequestModel(idToken: idToken);
      final result = await _authRepository.tokenWithApple(request);

      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        if (result.data.tmpToken?.isNotEmpty == true) {
          SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
        }

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.social,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  void loginWithWallet({
    required String address,
    required String signature,
  }) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = WalletLoginRequestModel(
        address: address,
        signature: signature,
        walletProvider: "metamask",
      );
      final result = await _authRepository.loginWithWallet(request);
      if (result.data.tmpToken?.isNotEmpty == true) {
        SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
      }
      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        SharedPref.setString(KeyShared.walletCurrentKey, address);

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.wallet,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  Future<String?> getNonceMessageForWalletSignature(String address) async {
    try {
      final result = await _authRepository.getNonceMessageForWalletSignature(
        address,
      );
      return result.data.message;
    } catch (e) {
      rethrow;
    }
  }
}
