import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/post_request.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';

part 'create_post_state.dart';

class CreatePostCubit extends Cubit<CreatePostState> {
  CreatePostCubit() : super(const CreatePostState());
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  final ImagePicker _imagePicker = ImagePicker();

  Future<void> pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      if (images.isNotEmpty) {
        final imagePaths = images.map((image) => image.path).toList();
        final currentPaths = List<String>.from(state.selectedImagePaths);
        currentPaths.addAll(imagePaths);
        emit(
          state.copyWith(
            selectedImagePaths: currentPaths,
            imageUploadStatus: ImageUploadStatus.initial,
            imageUploadError: null,
          ),
        );
      }
    } catch (e) {
      print('Error picking images: $e');
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to pick images: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      if (image != null) {
        final currentPaths = List<String>.from(state.selectedImagePaths);
        currentPaths.add(image.path);
        emit(
          state.copyWith(
            selectedImagePaths: currentPaths,
            imageUploadStatus: ImageUploadStatus.initial,
            imageUploadError: null,
          ),
        );
      }
    } catch (e) {
      print('Error taking photo: $e');
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to take photo: ${e.toString()}',
        ),
      );
    }
  }

  void removeImage(int index, bool isLocal) {
    if (isLocal) {
      final currentPaths = List<String>.from(state.selectedImagePaths);
      if (index >= 0 && index < currentPaths.length) {
        currentPaths.removeAt(index);
        emit(state.copyWith(selectedImagePaths: currentPaths));
      }
    } else {
      final currentUrls = List<String>.from(state.existingMediaUrls);
      if (index >= 0 && index < currentUrls.length) {
        currentUrls.removeAt(index);
        // When removing a remote image, we also need to remove its corresponding key
        final updatedKeys = List<String>.from(state.uploadedMediaKeys);
        if (index < updatedKeys.length) {
          updatedKeys.removeAt(index);
        }
        emit(
          state.copyWith(
            existingMediaUrls: currentUrls,
            uploadedMediaKeys: updatedKeys,
          ),
        );
      }
    }
  }

  Future<void> uploadImages() async {
    if (state.selectedImagePaths.isEmpty) return;

    emit(state.copyWith(imageUploadStatus: ImageUploadStatus.loading));

    try {
      final List<String> mediaKeys = [];

      for (final imagePath in state.selectedImagePaths) {
        final file = File(imagePath);
        final response = await _socialRepository.uploadMedia(file, 'image');
        mediaKeys.add(response.data.key ?? '');
      }

      // Combine newly uploaded keys with existing keys
      final updatedKeys = List<String>.from(state.uploadedMediaKeys);
      updatedKeys.addAll(mediaKeys);

      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.success,
          uploadedMediaKeys: updatedKeys,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to upload images',
        ),
      );
    }
  }

  void setPrivacy(PrivacyOption privacy) {
    emit(state.copyWith(privacy: privacy));
  }

  Future<void> createAPost({String? content}) async {
    emit(state.copyWith(status: CreatePostStatus.loading));
    try {
      // Upload images first if any are selected
      if (state.selectedImagePaths.isNotEmpty &&
          state.uploadedMediaKeys.isEmpty) {
        await uploadImages();
        if (state.imageUploadStatus == ImageUploadStatus.failure) {
          emit(
            state.copyWith(
              status: CreatePostStatus.failure,
              errorMessage: state.imageUploadError ?? 'Failed to upload images',
            ),
          );
          return;
        }
      }

      final request = CreatePostRequestModel(
        content: content,
        privacy: state.privacy.apiValue,
        mediaKeys: state.uploadedMediaKeys,
      );
      final post = await _socialRepository.createNewPost(request);

      emit(state.copyWith(status: CreatePostStatus.success, post: post.data));
    } catch (e) {
      emit(
        state.copyWith(
          status: CreatePostStatus.failure,
          errorMessage: 'Failed to create post',
        ),
      );
    }
  }

  void initUpdatePost(PostModel? post) {
    if (post == null) return;

    // Initialize the state with the post's existing data
    emit(
      state.copyWith(
        privacy: PrivacyOptionExt.fromApiValue(post.privacy),
        uploadedMediaKeys: post.mediaKeys,
        existingMediaUrls: post.mediaUrls,
      ),
    );
  }

  Future<void> updatePost({
    required String id,
    String? content,
    String? privacy,
  }) async {
    emit(state.copyWith(status: CreatePostStatus.loading));
    try {
      // Upload images first if any are selected
      if (state.selectedImagePaths.isNotEmpty) {
        await uploadImages();
        if (state.imageUploadStatus == ImageUploadStatus.failure) {
          emit(
            state.copyWith(
              status: CreatePostStatus.failure,
              errorMessage: state.imageUploadError ?? 'Failed to upload images',
            ),
          );
          return;
        }
      }

      // Use the current state's uploadedMediaKeys which should contain both
      // existing and newly uploaded media keys
      final postModel = PostRequest(
        content: content,
        mediaKeys: state.uploadedMediaKeys,
        privacy: privacy ?? state.privacy.apiValue,
      );

      await _socialRepository.updatePost(id, postModel);
      emit(state.copyWith(status: CreatePostStatus.success));
    } catch (e) {
      emit(
        state.copyWith(
          status: CreatePostStatus.failure,
          errorMessage: 'Failed to update post: ${e.toString()}',
        ),
      );
    }
  }
}
