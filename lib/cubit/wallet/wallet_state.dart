part of 'wallet_cubit.dart';

class WalletState extends Equatable {
  final ChainModel currentActiveChain;
  final List<TokenItemModel> tokens;
  const WalletState({required this.currentActiveChain, this.tokens = const []});
  @override
  List<Object?> get props => [currentActiveChain, tokens];

  WalletState copyWith({
    ChainModel? currentActiveChain,
    List<TokenItemModel>? tokens,
  }) {
    return WalletState(
      currentActiveChain: currentActiveChain ?? this.currentActiveChain,
      tokens: tokens ?? this.tokens,
    );
  }
}
